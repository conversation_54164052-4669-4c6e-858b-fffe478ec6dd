import Button from "./Button";
import "./HomepageMobile.css";
import Navbar from "./Navbar";
import WhyUs from "./WhyUs";
import MarketGrowth from "./MarketGrowth";
import WaitlistPopup from "./WaitlistPopup";
import Footer from "./Footer";
import ScrollProgressIndicator from "./ScrollProgressIndicator";
import { useState, useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register ScrollTrigger
gsap.registerPlugin(ScrollTrigger);

interface HomepageMobileProps {
  onJoinWaitlist: () => void;
}

const HomepageMobile = ({ onJoinWaitlist }: HomepageMobileProps) => {
  const [isWaitlistPopupOpen, setIsWaitlistPopupOpen] = useState(false);
  const [hoveredFeature, setHoveredFeature] = useState<number | null>(null);
  const [dialogPosition, setDialogPosition] = useState({ x: 0, y: 0 });
  const [activeFeatureIndex, setActiveFeatureIndex] = useState(0);
  const [activeFoundingPerkIndex, setActiveFoundingPerkIndex] = useState(0);
  const [currentDividerTextIndex, setCurrentDividerTextIndex] = useState(0);
  const [currentActiveSection, setCurrentActiveSection] = useState<string | null>(null);

  const handleJoinWaitlist = () => {
    setIsWaitlistPopupOpen(true);
  };

  const handleCloseWaitlistPopup = () => {
    setIsWaitlistPopupOpen(false);
  };

  // Divider text array for rotation (same as desktop)
  const dividerTexts = [
    "For the time you lose to non-billable work.",
    "For your rightful place in the creator economy.",
    "For a professional brand that's scattered and unseen.",
    "We are For the 90% of India that doesn't speak English as a first language."
  ];

  // Founding Member Perks data (same as desktop)
  const foundingMemberPerks = [
    {
      title: "Founding Member Badge",
      description: "Claim the permanent 'Founding Member' badge, a mark of distinction no one else can ever earn. Solidify your legacy as a pioneer in India's new legal economy."
    },
    {
      title: "Early Access & Homepage Feature",
      description: "Get every groundbreaking feature before the public and be featured on our homepage. This is your unignorable advantage to build instant authority and dominate the digital space."
    },
    {
      title: "Premium Access & Exclusive Events",
      description: "Enjoy 3 months of unrestricted premium access to build your digital brand, on us. Plus, get an exclusive seat at closed-door strategy events with legal industry titans."
    },
    {
      title: "Lifetime Status & Inner Circle",
      description: "Become a Founding Member for a lifetime of status and a perpetual head start. Secure your place in the inner circle before the door closes forever."
    }
  ];

  // Dialog content for each feature
  const dialogContent = {
    0: { // Features
      title: "Features",
      items: [
        "The 60% Solution: Your AI Co-Pilot",
        "Beyond the Billable Hour: The Monetization Hub",
        "Your Digital Gavel: The Automated Portfolio",
        "An All-India Reach: The Multilingual Engine"
      ]
    },
    1: { // Why You Should Join Us
      title: "Why You Should Join Us",
      items: [
        "Claim the permanent 'Founding Member' badge, a mark of distinction no one else can ever earn. Solidify your legacy as a pioneer in India's new legal economy.",
        "Get every groundbreaking feature before the public and be featured on our homepage. This is your unignorable advantage to build instant authority and dominate the digital space.",
        "Enjoy 3 months of unrestricted premium access to build your digital brand, on us. Plus, get an exclusive seat at closed-door strategy events with legal industry titans.",
        "Become a Founding Member for a lifetime of status and a perpetual head start. Secure your place in the inner circle before the door closes forever."
      ]
    },
    2: { // Testimonials
      title: "Testimonials",
      items: [
        "This is great",
        "This is awesome",
        "This is nice"
      ]
    }
  };

  // Handle feature item hover
  const handleFeatureHover = (index: number, event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setDialogPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    });
    setHoveredFeature(index);
  };

  const handleFeatureLeave = () => {
    setHoveredFeature(null);
  };

  // Divider text rotation effect with GSAP animation (same as desktop)
  useEffect(() => {
    let dividerTimeline: gsap.core.Timeline | null = null;

    const startDividerRotation = () => {
      const dividerElements = document.querySelectorAll('.homepage__divider-text');
      if (dividerElements.length === 0) return;

      // Create infinite timeline for divider text rotation
      dividerTimeline = gsap.timeline({
        repeat: -1,
        repeatDelay: 0
      });

      // Create a single rotation cycle that repeats
      dividerTimeline
        // Hold current text for 3 seconds
        .to(dividerElements, { duration: 3 })
        // Fade out with smooth animation
        .to(dividerElements, {
          opacity: 0,
          y: -10,
          duration: 0.3,
          ease: "power2.in"
        })
        // Update to next text
        .call(() => {
          setCurrentDividerTextIndex((prevIndex) => {
            return (prevIndex + 1) % dividerTexts.length;
          });
        })
        // Fade in new text with smooth animation
        .to(dividerElements, {
          opacity: 1,
          y: 0,
          duration: 0.4,
          ease: "power2.out"
        });
    };

    // Start the rotation after a short delay to ensure elements are rendered
    const timeoutId = setTimeout(startDividerRotation, 1000);

    return () => {
      clearTimeout(timeoutId);
      if (dividerTimeline) {
        dividerTimeline.kill();
      }
    };
  }, [dividerTexts.length]);

  // Mobile-optimized GSAP animations
  useEffect(() => {
    // Set initial states
    gsap.set([
      ".homepage-mobile__hero-title",
      ".homepage-mobile__feature-item",
      ".homepage-mobile__section-title",
      ".homepage__testimonials-title",
      ".homepage-mobile__team-title",
      ".homepage-mobile__team-card",
      ".testimonialimage",
      ".quote-word"
    ], {
      opacity: 0,
      y: 30
    });

    // Set initial state for divider text elements
    gsap.set(".homepage__divider-text", {
      opacity: 1,
      y: 0
    });

    // Hero title animation
    gsap.to(".homepage-mobile__hero-title", {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: "power2.out",
      delay: 0.3
    });

    // Feature items animation
    gsap.to(".homepage-mobile__feature-item", {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power2.out",
      stagger: 0.2,
      delay: 0.8
    });

    // Section titles with ScrollTrigger
    const sectionTitles = gsap.utils.toArray(".homepage-mobile__section-title, .homepage__testimonials-title, .homepage-mobile__team-title") as Element[];
    sectionTitles.forEach((element) => {
      gsap.to(element, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: element,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });
    });

    // Desktop testimonials animations for mobile
    gsap.to(".testimonialimage", {
      opacity: 1,
      scale: 1,
      duration: 1,
      ease: "power2.out",
      scrollTrigger: {
        trigger: ".homepage4",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    gsap.to(".quote-word", {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ease: "power2.out",
      stagger: 0.1,
      scrollTrigger: {
        trigger: ".homepage4",
        start: "top 70%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    // Mobile Features Section Pinned Scroll Animation
    let mobileCurrentActiveFeature = 0;
    let mobileIsTransitioning = false;
    let mobileActiveTimeline: gsap.core.Timeline | null = null;

    // Function to cleanup mobile animations
    const cleanupMobileAnimations = () => {
      if (mobileActiveTimeline) {
        mobileActiveTimeline.kill();
        mobileActiveTimeline = null;
      }
    };

    // Function to transition to a specific mobile feature
    const transitionToMobileFeature = (targetFeature: number) => {
      if (mobileIsTransitioning || targetFeature === mobileCurrentActiveFeature) return;

      mobileIsTransitioning = true;
      cleanupMobileAnimations();

      const mobileFeatureContents = document.querySelectorAll('.homepage-mobile__feature-content-item');
      const featureImage = document.querySelector('.homepage-mobile__feature-image') as HTMLElement;

      // Create timeline for synchronized transitions
      mobileActiveTimeline = gsap.timeline({
        onComplete: () => {
          // Update CSS classes for proper state management
          mobileFeatureContents.forEach((content, index) => {
            const element = content as HTMLElement;
            if (index === targetFeature) {
              element.classList.add('active');
            } else {
              element.classList.remove('active');
            }
          });

          mobileCurrentActiveFeature = targetFeature;
          mobileIsTransitioning = false;
          mobileActiveTimeline = null;
        }
      });

      // First, fade out current feature if it's different
      if (mobileCurrentActiveFeature !== targetFeature) {
        const currentElement = mobileFeatureContents[mobileCurrentActiveFeature] as HTMLElement;
        if (currentElement) {
          mobileActiveTimeline.to(currentElement, {
            opacity: 0,
            y: -10,
            duration: 0.25,
            ease: "power2.in"
          });
        }
      }

      // Simultaneously change background image and fade in new content
      mobileActiveTimeline.call(() => {
        if (featureImage) {
          const imageUrls = ['/feature1.png', '/feature2.png', '/feature3.png', '/feature4.png'];
          featureImage.style.backgroundImage = `url("${imageUrls[targetFeature]}")`;
        }
      })
      .to(mobileFeatureContents[targetFeature], {
        opacity: 1,
        y: 0,
        duration: 0.35,
        ease: "power2.out"
      }, mobileCurrentActiveFeature !== targetFeature ? "-=0.05" : "0"); // Slight overlap only if transitioning
    };

    ScrollTrigger.create({
      trigger: ".homepage-mobile__content-section",
      start: "top top",
      end: "+=300%",
      pin: true,
      scrub: 1,
      onEnter: () => {
        setCurrentActiveSection('features');
      },
      onUpdate: (self) => {
        const progress = self.progress;
        let targetFeature = 0;

        // Determine target feature based on progress with precise thresholds
        if (progress >= 0.75) {
          targetFeature = 3;
        } else if (progress >= 0.5) {
          targetFeature = 2;
        } else if (progress >= 0.25) {
          targetFeature = 1;
        } else {
          targetFeature = 0;
        }

        // Update scroll progress indicator
        setActiveFeatureIndex(targetFeature);

        // Only transition if we've crossed a threshold
        transitionToMobileFeature(targetFeature);
      },
      onLeave: () => {
        // Cleanup when leaving the section
        cleanupMobileAnimations();
        mobileIsTransitioning = false;
        setCurrentActiveSection(null);
      },
      onEnterBack: () => {
        // Reset state when entering back
        setCurrentActiveSection('features');
        mobileCurrentActiveFeature = 0;
        mobileIsTransitioning = false;
        cleanupMobileAnimations();
      }
    });

    // Initialize mobile feature content items to hidden state (except first one)
    gsap.set(".homepage-mobile__feature-content-item", { opacity: 0, y: 20 });
    gsap.set(".homepage-mobile__feature-content-item:first-child", { opacity: 1, y: 0 });

    // Set initial CSS classes for proper state management
    const initialMobileFeatureContents = document.querySelectorAll('.homepage-mobile__feature-content-item');
    initialMobileFeatureContents.forEach((content, index) => {
      const element = content as HTMLElement;
      if (index === 0) {
        element.classList.add('active');
      } else {
        element.classList.remove('active');
      }
    });

    // Team card animation
    gsap.to(".homepage-mobile__team-card", {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: "power2.out",
      scrollTrigger: {
        trigger: ".homepage-mobile__team",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    // Initialize Founding Member Perks section elements to hidden state
    gsap.set(".founding-member-perks__content-item", { opacity: 0, y: 50 });
    gsap.set(".founding-member-perks__title", { opacity: 0, y: 30 });
    // Set first perk to visible state
    gsap.set(".founding-member-perks__content-item:first-child", { opacity: 1, y: 0 });

    // Mobile Founding Member Perks Section with Pinned Scroll
    let mobileCurrentActivePerk = 0;
    let mobileIsTransitioningPerk = false;
    let mobileActiveTimelinePerk: gsap.core.Timeline | null = null;

    // Function to cleanup mobile perk animations
    const cleanupMobilePerkAnimations = () => {
      if (mobileActiveTimelinePerk) {
        mobileActiveTimelinePerk.kill();
        mobileActiveTimelinePerk = null;
      }
    };

    // Function to transition to a specific perk on mobile
    const transitionToMobilePerk = (targetPerk: number) => {
      if (mobileIsTransitioningPerk || mobileCurrentActivePerk === targetPerk) return;

      mobileIsTransitioningPerk = true;
      cleanupMobilePerkAnimations();

      const perkContents = document.querySelectorAll('.founding-member-perks__content-item');

      mobileActiveTimelinePerk = gsap.timeline({
        onComplete: () => {
          mobileCurrentActivePerk = targetPerk;
          mobileIsTransitioningPerk = false;
        }
      });

      // Fade out current perk
      if (perkContents[mobileCurrentActivePerk]) {
        mobileActiveTimelinePerk.to(perkContents[mobileCurrentActivePerk], {
          opacity: 0,
          y: -30,
          duration: 0.3,
          ease: "power2.in"
        });
      }

      // Fade in new perk
      mobileActiveTimelinePerk.to(perkContents[targetPerk], {
        opacity: 1,
        y: 0,
        duration: 0.4,
        ease: "power2.out"
      }, "-=0.1");
    };

    ScrollTrigger.create({
      trigger: ".founding-member-perks-section",
      start: "top top",
      end: "+=300%",
      pin: true,
      scrub: 1,
      onEnter: () => {
        setCurrentActiveSection('foundingMemberPerks');
      },
      onUpdate: (self) => {
        const progress = self.progress;
        let targetPerk = 0;

        // Determine target perk based on progress with precise thresholds
        if (progress >= 0.75) {
          targetPerk = 3;
        } else if (progress >= 0.5) {
          targetPerk = 2;
        } else if (progress >= 0.25) {
          targetPerk = 1;
        } else {
          targetPerk = 0;
        }

        // Update scroll progress indicator
        setActiveFoundingPerkIndex(targetPerk);

        // Only transition if we've crossed a threshold
        transitionToMobilePerk(targetPerk);
      },
      onLeave: () => {
        // Cleanup when leaving the section
        cleanupMobilePerkAnimations();
        mobileIsTransitioningPerk = false;
        setCurrentActiveSection(null);
      },
      onEnterBack: () => {
        // Reset state when entering back
        setCurrentActiveSection('foundingMemberPerks');
        mobileCurrentActivePerk = 0;
        mobileIsTransitioningPerk = false;
        cleanupMobilePerkAnimations();
        // Reset scroll progress indicator
        setActiveFoundingPerkIndex(0);
        // Reset title and first perk visibility for smooth scroll-back
        gsap.set(".founding-member-perks__title", { opacity: 1, y: 0 });
        gsap.set(".founding-member-perks__content-item", { opacity: 0, y: 50 });
        gsap.set(".founding-member-perks__content-item:first-child", { opacity: 1, y: 0 });
      }
    });

    // Animate title on scroll into view
    gsap.to(".founding-member-perks__title", {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: "power2.out",
      scrollTrigger: {
        trigger: ".founding-member-perks-section",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    // Cleanup
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  // Use the same features array as desktop version
  const features = [
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/aa858a2f8ed0a134930f72e7fef50e364573ebfc?width=118",
      title: "Features",
      description: "Benefit of joining waiting list",
      iconClass: "homepage-mobile__feature-icon",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/9badbe856312fc48be0805e349db264b09d2846c?width=118",
      title: "Why You should Join us",
      description: "Urgency of joining waiting list",
      iconClass: "homepage-mobile__feature-icon",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118",
      title: "Testimonials",
      description: "Benefit of joining waiting list",
      iconClass: "homepage-mobile__feature-icon",
    },
  ];

  // Feature content for the animated sections (same as desktop)
  const featureContents = [
    {
      title: "The 60% Solution: Your AI Co-Pilot",
      bullets: [
        "You spend up to 60% of your time on research and content creation.",
        "Our legal-specific AI, trained on Indian compliance and frameworks, automates your drafting process",
        "It's more than a tool; it's an integrated engine for writing, research, and SEO optimization that frees you to focus on what truly matters—your expertise."
      ]
    },
    {
      title: "Beyond the Billable Hour: The Monetization Hub",
      bullets: [
        "The $191.6 billion creator economy has largely excluded legal professionals. We're correcting this.",
        "Our platform provides the first direct path from legal expertise to sustainable revenue.",
        "With a credit-based economy and tools for premium subscriptions, you can finally build income streams beyond the traditional billable hour."
      ]
    },
    {
      title: "Your Digital Gavel: The Automated Portfolio",
      bullets: [
        "While 73% of legal professionals use digital tools, they lack a single, comprehensive system to showcase their expertise.",
        "Our Automated Portfolio Builder creates a credible, SEO-optimized digital presence for you.",
        "Combined with our Professional Verification System, you don't just get found—you get trusted."
      ]
    },
    {
      title: "An All-India Reach: The Multilingual Engine",
      bullets: [
        "Your insights are too valuable to be confined to one language.",
        "Our platform's multi-language support allows you to create and disseminate your work across India's diverse linguistic landscape.",
        "Reach a wider audience, connect with more clients, and build a truly national reputation."
      ]
    }
  ];



  return (
    <div className="homepage-mobile">
      <Navbar onJoinWaitlist={handleJoinWaitlist} />

      {/* Hero Section */}
      <section className="homepage-mobile__hero">
        <div className="homepage-mobile__hero-content">
          <h1 className="homepage-mobile__hero-title">
            Be a legal entrepreneur
          </h1>

          <div className="homepage-mobile__features-list">
            {features.map((feature, index) => (
              <div
                key={index}
                className="homepage-mobile__feature-item"
                onMouseEnter={(e) => handleFeatureHover(index, e)}
                onMouseLeave={handleFeatureLeave}
              >
                <img
                  src={feature.icon}
                  alt=""
                  className={feature.iconClass}
                />
                <div className="homepage-mobile__feature-content">
                  <h3 className="homepage-mobile__feature-title">
                    {feature.title}
                  </h3>
                  <p className="homepage-mobile__feature-description">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Hover Dialog */}
          {hoveredFeature !== null && (
            <div
              className="homepage-mobile__hover-dialog"
              style={{
                position: 'fixed',
                left: `${dialogPosition.x}px`,
                top: `${dialogPosition.y}px`,
                transform: 'translate(-50%, -100%)',
                zIndex: 1000,
                pointerEvents: 'none'
              }}
            >
              <div className="homepage-mobile__dialog-content">
                <h4 className="homepage-mobile__dialog-title">
                  {dialogContent[hoveredFeature as keyof typeof dialogContent].title}
                </h4>
                <ul className="homepage-mobile__dialog-list">
                  {dialogContent[hoveredFeature as keyof typeof dialogContent].items.map((item, index) => (
                    <li key={index} className="homepage-mobile__dialog-item">
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          <div className="homepage-mobile__hero-bottom">
            <p className="homepage-mobile__hero-subtitle">
              Creator platform for modern — legal professionals
            </p>

            <Button size="large" onClick={handleJoinWaitlist}>
              Join Waitlist
            </Button>
          </div>
        </div>
      </section>

      {/* Decorative Strip Divider */}
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-text">
            {dividerTexts[currentDividerTextIndex]}
          </div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>

      {/* Content Creation Section */}
      <section className="homepage-mobile__content-section">
        <div className="homepage-mobile__feature-image"></div>

        {/* Scroll Progress Indicator for Mobile Features */}
        <ScrollProgressIndicator
          activeIndex={activeFeatureIndex}
          totalSections={4}
          sectionTitles={[
            "AI Co-Pilot",
            "Monetization Hub",
            "Automated Portfolio",
            "Multilingual Engine"
          ]}
          visible={currentActiveSection === 'features'}
        />

        <div className="homepage-mobile__content-text">
          {featureContents.map((feature, index) => (
            <div key={index} className="homepage-mobile__feature-content-item" style={{ position: 'absolute', width: '100%' }}>
              <h2 className="homepage-mobile__section-title">
                {feature.title.split(':')[0]}:<br />{feature.title.split(':')[1]}
              </h2>

              <div className="homepage-mobile__content-features">
                {feature.bullets.map((bullet, bulletIndex) => (
                  <div key={bulletIndex} className="homepage-mobile__content-feature">
                    <span className="homepage-mobile__content-icon">•</span>
                    <p className="homepage-mobile__content-description">
                      {bullet}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Decorative Strip Divider */}
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-text">
            {dividerTexts[currentDividerTextIndex]}
          </div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>

      {/* Market Growth Section */}
      <MarketGrowth />

      {/* Decorative Strip Divider */}
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-text">
            {dividerTexts[currentDividerTextIndex]}
          </div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>

      {/* Why Us Section */}
      <WhyUs />

      {/* Decorative Strip Divider */}
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-text">
            {dividerTexts[currentDividerTextIndex]}
          </div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>

      {/* Testimonials Section - Using Desktop Implementation */}
      <div id="testimonials" className="homepage4">
        <div className="homepage__testimonials-content">
          <div className="homepage__testimonials-header">
            <div className="homepage__testimonials-title">
              Our Customer's Opinions
            </div>
            <div className="homepage__testimonials-quote-mark"></div>
          </div>
          <div className="homepage__testimonials-quote-box">
            <div className="homepage__testimonials-quote-text">
              <span className="quote-word">Extensive</span>{" "}
              <span className="quote-word">substantive</span>{" "}
              <span className="quote-word">knowledge,</span>{" "}
              <span className="quote-word">extensive</span>{" "}
              <span className="quote-word">experience,</span>{" "}
              <span className="quote-word">reliability,</span>{" "}
              <span className="quote-word">commitment</span>{" "}
              <span className="quote-word">and</span>{" "}
              <span className="quote-word">availability</span>{" "}
              <span className="quote-word">of</span>{" "}
              <span className="quote-word">a</span>{" "}
              <span className="quote-word">team</span>{" "}
              <span className="quote-word">of</span>{" "}
              <span className="quote-word">specialists</span>{" "}
              <span className="quote-word">ensure</span>{" "}
              <span className="quote-word">the</span>{" "}
              <span className="quote-word">highest</span>{" "}
              <span className="quote-word">level</span>{" "}
              <span className="quote-word">of</span>{" "}
              <span className="quote-word">service.</span>
            </div>
          </div>
        </div>
        <div className="homepage__hero2">
          <div className="testimonialimage"></div>
          <div className="homepage__subhero"></div>
        </div>
      </div>

      {/* Team Testimonial Section - Desktop Style */}
      <div className="homepage__team-testimonial-section">
        <div className="homepage__team-testimonial-background">
          <div
            dangerouslySetInnerHTML={{
              __html:
                '<svg id="210:579" width="799" height="871" viewBox="0 0 799 871" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="background-svg" style="width: 799px; height: 100vh; display: block"> <foreignObject x="0" y="0" width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(0px);clip-path:url(#bgblur_0_210_579_clip_path);height:100vh%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="0" d="M0 0H525V871H0V0Z" fill="#E5CCA4"></path> <foreignObject x="340.3" y="128.8" width="469.4" height="613.4"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5.35px);clip-path:url(#bgblur_1_210_579_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="10.7" d="M351 139.5H799V731.5H351V139.5Z" fill="#F9EDE9"></path> <rect x="351" y="139.5" width="448" height="592" fill="url(#pattern0_210_579)"></rect> <defs> <clipPath id="bgblur_0_210_579_clip_path" transform="translate(0 0)"><path d="M0 0H525V871H0V0Z"></path> </clipPath><clipPath id="bgblur_1_210_579_clip_path" transform="translate(-340.3 -128.8)"><path d="M351 139.5H799V731.5H351V139.5Z"></path> </clipPath><pattern id="pattern0_210_579" patternContentUnits="objectBoundingBox" width="1" height="1"> <use xlink:href="#image0_210_579" transform="matrix(0.00172797 0 0 0.00130828 -0.426075 -0.278796)"></use> </pattern>  </defs> </svg>',
            }}
          />
        </div>
        <div className="homepage__team-testimonial-content">
          <div className="homepage__team-testimonial-ratings">
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/862c345882334d9eb32a40f2d45f9ec9b9265f5e?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
          </div>
          <div className="homepage__team-testimonial-text-content">
            <div className="homepage__team-testimonial-title">
              What our team has to say!
            </div>
            <div className="homepage__team-testimonial-quote">
              &quot;Amplify the digital footprint of contributing legal experts.
              Detailed author profiles and multi-channel promotion boost their
              professional authority.&quot;
            </div>
          </div>
          <div className="homepage__team-testimonial-attribution">
            <div className="homepage__team-testimonial-author">
              This is Sahil Saurav. Meet the Whole team
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Strip Divider */}
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-text">
            {dividerTexts[currentDividerTextIndex]}
          </div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>

      {/* Founding Member Perks Section */}
      <div id="founding-member-perks" className="founding-member-perks-section">
        <div className="founding-member-perks__container">
          <div className="founding-member-perks__title">
            Founding Member Perks
          </div>

          <div className="founding-member-perks__content">
            {foundingMemberPerks.map((perk, index) => (
              <div
                key={index}
                className="founding-member-perks__content-item"
                style={{ position: 'absolute', width: '100%' }}
              >
                <div className="founding-member-perks__perk-title">
                  {perk.title}
                </div>
                <div className="founding-member-perks__perk-description">
                  {perk.description}
                </div>
              </div>
            ))}
          </div>

          {/* Scroll Progress Indicator for Founding Member Perks */}
          <ScrollProgressIndicator
            activeIndex={activeFoundingPerkIndex}
            totalSections={4}
            sectionTitles={[
              "Founding Member Badge",
              "Early Access & Homepage Feature",
              "Premium Access & Exclusive Events",
              "Lifetime Status & Inner Circle"
            ]}
            visible={currentActiveSection === 'foundingMemberPerks'}
          />
        </div>
      </div>

      {/* Decorative Strip Divider */}
      <div className="homepage__divider-strip">
        <div className="homepage__divider-content">
          <div className="homepage__divider-text">
            {dividerTexts[currentDividerTextIndex]}
          </div>
        </div>
        <div className="homepage__divider-pattern">
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
          <div className="homepage__divider-dot"></div>
        </div>
      </div>

      {/* Team Section - Redesigned */}
      <section className="homepage-mobile__team">
        <div className="homepage-mobile__team-content">
          <h2 className="homepage-mobile__team-title">
            What our team has to say
          </h2>

          <div className="homepage-mobile__team-card">
            <div className="homepage-mobile__team-image-container">
              <img
                src="/team-group.jpg"
                alt="LawVriksh Team"
                className="homepage-mobile__team-image"
              />
              <div className="homepage-mobile__team-overlay">
                <span className="homepage-mobile__team-label">Our Legal Experts</span>
              </div>
            </div>

            <div className="homepage-mobile__team-quote">
              <div className="homepage-mobile__quote-mark">"</div>
              <p className="homepage-mobile__team-text">
                Amplify the digital footprint of contributing legal experts.
                Detailed author profiles and multi-channel promotion boost
                their professional authority.
              </p>
            </div>

            <div className="homepage-mobile__team-rating">
              
              <p className="homepage-mobile__rating-text">Trusted by Legal Professionals</p>
            </div>
          </div>
        </div>
      </section>

      <Footer />

      <WaitlistPopup
        isOpen={isWaitlistPopupOpen}
        onClose={handleCloseWaitlistPopup}
      />
    </div>
  );
};

export default HomepageMobile;
